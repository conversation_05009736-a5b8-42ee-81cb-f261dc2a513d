import { useCallback, useEffect, useMemo, useState, type ReactElement } from 'react'
import { isEmpty, isNil, uniq } from 'lodash'
import {
  Box,
  Button,
  ContainerWithTabsForDataGrid,
  DataGridAsTabItem,
  GRID_CHECKBOX_SELECTION_COL_DEF,
  GridActionsCellItem,
  GridToolbarWithQuickFilter,
  LinearProgress,
  MenuItem,
  MenuList,
  Popover,
  Tooltip,
  Typography,
  useDataGridColumnHelper,
  type GridColDef,
  type GridRowSelectionModel,
} from '@karoo-ui/core'
import AddIcon from '@mui/icons-material/Add'
import CloseIcon from '@mui/icons-material/Close'
import InfoIcon from '@mui/icons-material/Info'
import KeyIcon from '@mui/icons-material/Key'
import ThreeDotsIcon from '@mui/icons-material/MoreVert'
import TaskAltOutlinedIcon from '@mui/icons-material/TaskAltOutlined'
import { DateTime } from 'luxon'
import PopupState, { bindPopover, bindTrigger } from 'material-ui-popup-state'
import { useHistory, useRouteMatch } from 'react-router'
import { match, P } from 'ts-pattern'
import type { ValueOf } from 'type-fest'

import type { CarpoolBookingId, DriverId, VehicleId } from 'api/types'
import { buildRouteQueryString } from 'api/utils'
import { getAuthenticatedUser, getSettings_UNSAFE } from 'duxs/user'
import PageWithMainTableContainer from 'src/components/_containers/PageWithMainTable'
import { useDriversQuery } from 'src/modules/api/useDriversQuery'
import { DETAILS_PREFIX } from 'src/modules/app/components/routes/carpool'
import { UserDataGridWithSavedSettingsOnIDB } from 'src/modules/components/connected/UserDataGridWithSavedSettingsOnIDB'
import { useTypedSelector } from 'src/redux-hooks'
import type { ExcludeStrict } from 'src/types/utils'

import { anomonymizeIdPassport } from 'cartrack-utils'
import { ctIntl } from 'cartrack-ui-kit'
import CarpoolStatusChip from '../../components/CarpoolStatusChip'
import { issuanceRequestSearchParamsSchema } from '../../components/SpfIssuanceRequestDrawer/schema'
import StatBar from '../../components/StatBar'
import useCarpoolOptionsQuery, {
  type FetchCarpoolOptionsParsedData,
} from '../../queries/useCarpoolOptionsQuery'
import {
  useApproveActiveRule,
  useKeyCollectionRule,
  useRuleLoadingStatus,
} from '../../Settings/Rules/api/queries'
import { BookingStatus, CustomTabs } from '../../utils/constants'
import {
  CarpoolOptionsContext,
  customTabAndStatusMapping,
  LIST_TABS,
  TAB_OPTIONS,
} from '../constants'
import useBookingSubmissionsQuery, {
  type FormattedSubmitInfo,
} from './api/useBookingSubmissionsQuery'
import useIssuanceListQuery, {
  type Booking,
  type FetchIssuanceList,
  type Metric,
} from './api/useIssuanceListQuery'
import ApproveBookingModal from './components/ApproveBookingModal'
import BookingDetailsDrawer from './components/BookingDetailsDrawer'
import BookingSubmissionsChip from './components/BookingSubmissionsChip'
import CancelBookingModal from './components/CancelBookingModal'
import ChangeToActiveConfirmationModal from './components/ChangeToActiveModal'
import CompleteBookingModal from './components/CompleteBookingModal'
import DeclineBookingModal from './components/DeclineBookingModal'
import ForceTerminateBookingModal from './components/ForceTerminateBookingModal'
import KeyCollectionModal, {
  type FormPossibleValues as KeyCollectionFormPossibleValues,
} from './components/KeyCollectionModal'
import KeyReturnModal from './components/KeyReturnModal'

const metricConfig: Array<{ key: Metric; label: string; tab?: CustomTabs }> = [
  { key: 'TOTAL', label: 'Total' },
  { key: 'ACTIVE', label: 'Active', tab: CustomTabs.ACTIVE },
  { key: 'ACTIVE_LATE', label: 'Active Late', tab: CustomTabs.ACTIVE_LATE },
  { key: 'REQUESTED', label: 'Requested', tab: CustomTabs.REQUESTED },
  { key: 'APPROVED', label: 'global.approved', tab: CustomTabs.APPROVED },
  { key: 'DECLINED', label: 'Declined', tab: CustomTabs.DECLINED },
  { key: 'CANCELLED', label: 'Cancelled', tab: CustomTabs.CANCELLED },
  { key: 'FREE', label: 'Free', tab: CustomTabs.FREE },
  { key: 'RETURNED', label: 'Returned', tab: CustomTabs.RETURNED },
  { key: 'RETURNED_LATE', label: 'Returned Late', tab: CustomTabs.RETURNED_LATE },
  {
    key: 'FORCE_TERMINATED',
    label: 'Force Terminated',
    tab: CustomTabs.FORCE_TERMINATED,
  },
] as const

const columnsGetters = {
  bookingNumber: (booking: IssuanceItem) => booking.id,
  vehicle: (booking: IssuanceItem) => booking.vehicleRegistration ?? '',
  driverId: (booking: IssuanceItem) => booking.driverId ?? '',
  vehicleType: (booking: IssuanceItem) => booking.vehicleType ?? '',
  purpose: (booking: IssuanceItem) => booking.purpose ?? '',
  requestor: (booking: IssuanceItem) => booking.requestor,
  requestDate: (booking: IssuanceItem) =>
    booking.requestDate ? new Date(booking.requestDate) : null,
  startDate: (booking: IssuanceItem) =>
    booking.startDate ? new Date(booking.startDate) : null,
  pickUpAt: (booking: IssuanceItem) =>
    booking.pickupIgnitionTime ? new Date(booking.pickupIgnitionTime) : null,
  endDate: (booking: IssuanceItem) =>
    booking.endDate ? new Date(booking.endDate) : null,
  returnedAt: (booking: IssuanceItem) => {
    if (
      [
        BookingStatus.BOOKING_STATUS_RETURNED,
        BookingStatus.BOOKING_STATUS_RETURNED_LATE,
        BookingStatus.BOOKING_STATUS_FORCE_TERMINATED,
      ].includes(booking.statusId)
    ) {
      return booking.returnedIgnitionTime
        ? new Date(booking.returnedIgnitionTime)
        : null
    } else {
      return null
    }
  },
  statusId: (booking: IssuanceItem) => booking.statusId,
  status: (booking: IssuanceItem) => booking.status,
  type: (booking: IssuanceItem) => booking.type,
  approvedBy: (booking: IssuanceItem) => booking.approvedBy,
  declinedBy: (booking: IssuanceItem) => booking.declinedBy,
}

type IssuanceItem = Booking & {
  formSubmissions: Array<FormattedSubmitInfo>
}

const SpfList = () => {
  const history = useHistory()
  const { path } = useRouteMatch()
  const columnHelper = useDataGridColumnHelper<IssuanceItem>({ filterMode: 'client' })

  const [currentModal, setCurrentModal] = useState<
    | {
        type: 'key_return' | 'key_collection'
        data: {
          bookingId: CarpoolBookingId
          initialValues: KeyCollectionFormPossibleValues
        }
      }
    | {
        type:
          | 'force_terminate_booking'
          | 'cancel_booking'
          | 'decline_booking'
          | 'complete_booking'
          | 'approve_booking'
          | 'change_to_active'
        data: { bookingIds: Array<CarpoolBookingId> }
      }
    | null
  >(null)

  const [currentDrawer, setCurrentDrawer] = useState<{
    type: 'booking_details'
    data: {
      bookingId: CarpoolBookingId
      vehicleTimelineParams: {
        vehicleId: VehicleId
        startDate: string
        endDate: string
      }
    }
  } | null>(null)

  const [issuanceList, setIssuanceList] = useState<Array<IssuanceItem>>([])
  const [tabs, setTabs] = useState<
    ReadonlyArray<{
      label: string
      value: ValueOf<typeof LIST_TABS>
      icon?: ReactElement
      iconPosition?: 'bottom' | 'top' | 'end' | 'start' | undefined
    }>
  >(TAB_OPTIONS)
  const [currentTab, setCurrentTab] = useState(TAB_OPTIONS[0].value)
  const [customSelection, setCustomSelection] = useState<CustomTabs | ''>('')
  const [multiSelectedBookingIds, setMultiSelectedBookingIds] = useState<
    Array<CarpoolBookingId>
  >([])

  const {
    carpoolAppName,
    carpoolApproveBookings,
    carpoolDeclineBookings,
    carpoolEditBookings,
    carpoolChangeBookingToActive,
    carpoolCancelBooking,
    carpoolForceTerminateBooking,
    isAdmin,
  } = useTypedSelector(getSettings_UNSAFE)
  const { cuid: clientUserId } = useTypedSelector(getAuthenticatedUser)

  const driverQuery = useDriversQuery()
  const issuanceListQuery = useIssuanceListQuery()
  const bookingSubmissionsQuery = useBookingSubmissionsQuery()
  const bookingOptionQuery = useCarpoolOptionsQuery()
  const keyCollectionRule = useKeyCollectionRule()
  const activeBookingRule = useApproveActiveRule()
  const isRuleFetching = useRuleLoadingStatus()

  useEffect(() => {
    if (issuanceListQuery.data) {
      const submissionMap = bookingSubmissionsQuery.data
      setIssuanceList(
        issuanceListQuery.data.bookings.map((issuance) => ({
          ...issuance,
          formSubmissions: submissionMap ? submissionMap.get(issuance.id) ?? [] : [],
        })),
      )
    }
  }, [issuanceListQuery.data, bookingSubmissionsQuery.data])

  const canApproveBooking = useCallback(
    (pendingManagers: Array<Array<string>>) =>
      isAdmin ||
      isEmpty(pendingManagers) ||
      pendingManagers[0].includes(clientUserId ?? ''),
    [clientUserId, isAdmin],
  )

  const renderCompleteAction = useCallback(
    (bookingId: CarpoolBookingId) => (
      <Tooltip
        title={ctIntl.formatMessage({
          id: 'carpool.list.complete',
        })}
        arrow
        key="complete"
      >
        <span>
          <GridActionsCellItem
            label=""
            icon={<TaskAltOutlinedIcon />}
            onClick={() =>
              setCurrentModal({
                type: 'complete_booking',
                data: {
                  bookingIds: [bookingId],
                },
              })
            }
          />
        </span>
      </Tooltip>
    ),
    [],
  )

  const renderForceTerminateAction = useCallback(
    (bookingId: CarpoolBookingId) => (
      <Tooltip
        title={ctIntl.formatMessage({
          id: 'carpool.list.forceTerminateBooking',
        })}
        arrow
        key="forceTerminate"
      >
        <span>
          <GridActionsCellItem
            label=""
            icon={<CloseIcon />}
            onClick={() => {
              setCurrentModal({
                type: 'force_terminate_booking',
                data: {
                  bookingIds: [bookingId],
                },
              })
            }}
            disabled={!carpoolForceTerminateBooking}
          />
        </span>
      </Tooltip>
    ),
    [carpoolForceTerminateBooking],
  )

  const generateIssuanceActions = useCallback(
    (row: IssuanceItem) => {
      const {
        statusId,
        id: bookingId,
        driverId,
        vehicleId,
        vehicleTypeId,
        startDate,
        endDate,
        keyCollectionDate,
        keyReturnDate,
        pickupIgnitionTime,
        returnedIgnitionTime,
      } = row

      const initialValuesForKeyCollection: KeyCollectionFormPossibleValues = {
        driverId,
        vehicleId,
        vehicleType: vehicleTypeId,
      }

      const actualStartAndEndTime = match(statusId)
        .with(
          BookingStatus.BOOKING_STATUS_ACTIVE,
          BookingStatus.BOOKING_STATUS_ACTIVE_LATE,
          () => ({
            startDate: pickupIgnitionTime || startDate,
            endDate: DateTime.now().toString(),
          }),
        )
        .with(
          BookingStatus.BOOKING_STATUS_RETURNED,
          BookingStatus.BOOKING_STATUS_RETURNED_LATE,
          () => ({
            startDate: pickupIgnitionTime || startDate,
            endDate: returnedIgnitionTime || endDate,
          }),
        )
        .otherwise(() => ({
          startDate: pickupIgnitionTime || startDate,
          endDate,
        }))

      const vehicleTimelineParams = {
        // here vehicle id should not be null
        vehicleId: vehicleId as VehicleId,
        ...actualStartAndEndTime,
      }

      return match(statusId)
        .with(BookingStatus.BOOKING_STATUS_REQUESTED, () => [
          <GridActionsCellItem
            key="approve"
            label={ctIntl.formatMessage({ id: 'Approve' })}
            onClick={() =>
              setCurrentModal({
                type: 'approve_booking',
                data: {
                  bookingIds: [bookingId],
                },
              })
            }
            disabled={
              !carpoolApproveBookings || !canApproveBooking(row.pendingManagers)
            }
            showInMenu
          />,
          <GridActionsCellItem
            key="edit"
            label={ctIntl.formatMessage({ id: 'Edit' })}
            onClick={() => {
              history.push(
                `${path}/${DETAILS_PREFIX}?${buildRouteQueryString({
                  schema: issuanceRequestSearchParamsSchema,
                  searchParams: { type: 'edit', id: bookingId },
                })}`,
              )
            }}
            disabled={!carpoolEditBookings}
            showInMenu
          />,
          <GridActionsCellItem
            key="decline"
            label={ctIntl.formatMessage({ id: 'Decline' })}
            onClick={() =>
              setCurrentModal({
                type: 'decline_booking',
                data: { bookingIds: [bookingId] },
              })
            }
            disabled={!carpoolDeclineBookings}
            showInMenu
          />,
        ])
        .with(BookingStatus.BOOKING_STATUS_APPROVED, () => [
          keyCollectionRule ? (
            <Tooltip
              title={ctIntl.formatMessage({
                id: isNil(keyCollectionDate)
                  ? 'carpool.list.collectKey'
                  : 'carpool.list.keyIsCollected',
              })}
              key="pickupKey"
            >
              <span>
                <GridActionsCellItem
                  icon={<KeyIcon />}
                  label=""
                  onClick={() =>
                    setCurrentModal({
                      type: 'key_collection',
                      data: {
                        bookingId,
                        initialValues: initialValuesForKeyCollection,
                      },
                    })
                  }
                  disabled={!isNil(keyCollectionDate)}
                />
              </span>
            </Tooltip>
          ) : (
            <></>
          ),
          <GridActionsCellItem
            key="changeToActive"
            label={ctIntl.formatMessage({ id: 'carpool.list.changeToActive' })}
            onClick={() =>
              setCurrentModal({
                type: 'change_to_active',
                data: { bookingIds: [bookingId] },
              })
            }
            showInMenu
            disabled={!carpoolChangeBookingToActive}
          />,
          <GridActionsCellItem
            key="edit"
            label={ctIntl.formatMessage({ id: 'carpool.list.viewBookingDetail' })}
            onClick={() => {
              history.push(
                `${path}/${DETAILS_PREFIX}?${buildRouteQueryString({
                  schema: issuanceRequestSearchParamsSchema,
                  searchParams: { type: 'edit', id: bookingId },
                })}`,
              )
            }}
            showInMenu
          />,
          <GridActionsCellItem
            key="cancel"
            label={ctIntl.formatMessage({ id: 'Cancel' })}
            onClick={() =>
              setCurrentModal({
                type: 'cancel_booking',
                data: {
                  bookingIds: [bookingId],
                },
              })
            }
            showInMenu
            disabled={!carpoolCancelBooking}
          />,
        ])
        .with(
          BookingStatus.BOOKING_STATUS_ACTIVE,
          BookingStatus.BOOKING_STATUS_ACTIVE_LATE,
          () => {
            const additionalAction = (() => {
              if (isRuleFetching) {
                return <></>
              }
              return match(activeBookingRule)
                .with(P.union('checklist', 'geofence'), () =>
                  renderForceTerminateAction(bookingId),
                )
                .with('keyCollection', () => (
                  <>
                    {renderForceTerminateAction(bookingId)}
                    {keyCollectionRule ? (
                      <Tooltip
                        title={ctIntl.formatMessage({
                          id: 'carpool.list.returnKey',
                        })}
                        arrow
                        key="returnKey"
                      >
                        <span>
                          <GridActionsCellItem
                            icon={<KeyIcon />}
                            label=""
                            onClick={() => {
                              setCurrentModal({
                                type: 'key_return',
                                data: {
                                  bookingId,
                                  initialValues: initialValuesForKeyCollection,
                                },
                              })
                            }}
                          />
                        </span>
                      </Tooltip>
                    ) : (
                      <></>
                    )}
                  </>
                ))
                .with(P.union('manual'), () => (
                  <>
                    {renderCompleteAction(bookingId)}
                    {renderForceTerminateAction(bookingId)}
                  </>
                ))
                .exhaustive()
            })()
            return [
              additionalAction,
              <Tooltip
                title={ctIntl.formatMessage({ id: 'Info' })}
                key="info"
              >
                <span>
                  <GridActionsCellItem
                    icon={<InfoIcon />}
                    label=""
                    onClick={() =>
                      setCurrentDrawer({
                        type: 'booking_details',
                        data: { bookingId, vehicleTimelineParams },
                      })
                    }
                  />
                </span>
              </Tooltip>,
            ]
          },
        )
        .with(
          BookingStatus.BOOKING_STATUS_CANCELLED,
          BookingStatus.BOOKING_STATUS_RETURNED,
          BookingStatus.BOOKING_STATUS_RETURNED_LATE,
          () => [
            keyCollectionRule ? (
              <Tooltip
                title={ctIntl.formatMessage({
                  id: isNil(keyReturnDate)
                    ? 'carpool.list.returnKey'
                    : 'carpool.list.keyIsReturned',
                })}
                key="returnKey"
              >
                <span>
                  <GridActionsCellItem
                    icon={<KeyIcon />}
                    label=""
                    onClick={() => {
                      setCurrentModal({
                        type: 'key_return',
                        data: {
                          bookingId,
                          initialValues: initialValuesForKeyCollection,
                        },
                      })
                    }}
                    disabled={!isNil(keyReturnDate) || isNil(keyCollectionDate)}
                  />
                </span>
              </Tooltip>
            ) : (
              <></>
            ),
            <Tooltip
              title={ctIntl.formatMessage({ id: 'Info' })}
              key="info"
            >
              <span>
                <GridActionsCellItem
                  icon={<InfoIcon />}
                  label={ctIntl.formatMessage({ id: 'Info' })}
                  onClick={() =>
                    setCurrentDrawer({
                      type: 'booking_details',
                      data: { bookingId, vehicleTimelineParams },
                    })
                  }
                />
              </span>
            </Tooltip>,
          ],
        )
        .with(
          BookingStatus.BOOKING_STATUS_DECLINED,
          BookingStatus.BOOKING_STATUS_FORCE_TERMINATED,
          () => [
            <Tooltip
              title={ctIntl.formatMessage({ id: 'Info' })}
              key="info"
            >
              <span>
                <GridActionsCellItem
                  icon={<InfoIcon />}
                  label={ctIntl.formatMessage({ id: 'Info' })}
                  onClick={() =>
                    setCurrentDrawer({
                      type: 'booking_details',
                      data: { bookingId, vehicleTimelineParams },
                    })
                  }
                />
              </span>
            </Tooltip>,
          ],
        )
        .otherwise(() => [])
    },
    [
      activeBookingRule,
      canApproveBooking,
      carpoolApproveBookings,
      carpoolCancelBooking,
      carpoolChangeBookingToActive,
      carpoolDeclineBookings,
      carpoolEditBookings,
      history,
      isRuleFetching,
      keyCollectionRule,
      path,
      renderCompleteAction,
      renderForceTerminateAction,
    ],
  )

  const columns = useMemo((): Array<GridColDef<IssuanceItem>> => {
    type DriverMap = ExcludeStrict<
      (typeof driverQuery)['data'],
      undefined
    >['allDriversById']
    const allDriversById = driverQuery.data?.allDriversById ?? (new Map() as DriverMap)

    return [
      columnHelper.string((_, row) => columnsGetters.bookingNumber(row), {
        field: 'bookingNumber',
        headerName: ctIntl.formatMessage({ id: 'carpool.list.bookingNumber' }),
        align: 'left',
      }),
      columnHelper.string((_, row) => columnsGetters.vehicle(row), {
        field: 'vehicle',
        headerName: ctIntl.formatMessage({ id: 'Vehicle' }),
      }),
      columnHelper.string(
        (_, row) => {
          const driverId = columnsGetters.driverId(row)
          const driver = allDriversById.get(driverId as DriverId)
          if (!driverId || !driver) {
            return ''
          }

          const idNumber = driver.idPassportNumber

          return `${driver.name}${
            idNumber ? ' (' + anomonymizeIdPassport(idNumber) + ')' : ''
          }`
        },
        {
          field: 'driver',
          headerName: ctIntl.formatMessage({ id: 'Driver' }),
        },
      ),
      columnHelper.string((_, row) => columnsGetters.vehicleType(row), {
        field: 'vehicleType',
        headerName: ctIntl.formatMessage({ id: 'carpool.vehicleCategory' }),
      }),
      columnHelper.string((_, row) => columnsGetters.purpose(row), {
        field: 'purpose',
        headerName: ctIntl.formatMessage({ id: 'carpool.list.purpose' }),
      }),
      columnHelper.string((_, row) => columnsGetters.requestor(row), {
        field: 'requestor',
        headerName: ctIntl.formatMessage({ id: 'carpool.bookingDetails.requestor' }),
      }),
      columnHelper.dateTime({
        field: 'requestDate',
        headerName: ctIntl.formatMessage({ id: 'Request Date' }),
        valueGetter: (_, row) => columnsGetters.requestDate(row),
      }),
      columnHelper.dateTime({
        field: 'startDate',
        headerName: ctIntl.formatMessage({ id: 'Start Date' }),
        valueGetter: (_, row) => columnsGetters.startDate(row),
      }),
      columnHelper.dateTime({
        field: 'pickUpAt',
        headerName: ctIntl.formatMessage({ id: 'Pick Up At' }),
        valueGetter: (_, row) => columnsGetters.pickUpAt(row),
      }),
      columnHelper.dateTime({
        field: 'endDate',
        headerName: ctIntl.formatMessage({ id: 'End Date' }),
        valueGetter: (_, row) => columnsGetters.endDate(row),
      }),
      columnHelper.dateTime({
        field: 'returnedAt',
        headerName: ctIntl.formatMessage({ id: 'Returned At' }),
        valueGetter: (_, row) => columnsGetters.returnedAt(row),
      }),
      columnHelper.valueGetter((_, row) => columnsGetters.status(row), {
        field: 'status',
        headerName: ctIntl.formatMessage({ id: 'Status' }),
        renderCell: ({ row }) => (
          <CarpoolStatusChip
            bookingStatusId={columnsGetters.statusId(row) as BookingStatus}
            approvalProcess={{
              approvalsNeeded: row.approvalsNeeded,
              approvedCount: row.approvedCount,
            }}
          />
        ),
        minWidth: 200,
      }),
      columnHelper.string((_, row) => columnsGetters.type(row), {
        field: 'type',
        headerName: ctIntl.formatMessage({ id: 'Type' }),
        flex: 1,
      }),
      columnHelper.string((_, row) => columnsGetters.approvedBy(row), {
        field: 'approvedBy',
        headerName: ctIntl.formatMessage({ id: 'Approved By' }),
      }),
      columnHelper.string((_, row) => columnsGetters.declinedBy(row), {
        field: 'declinedBy',
        headerName: ctIntl.formatMessage({ id: 'Declined By' }),
      }),
      {
        field: 'actions',
        type: 'actions',
        headerName: ctIntl.formatMessage({ id: 'Actions' }),
        minWidth: 120,
        align: 'right',
        getActions: ({ row }) => [
          <BookingSubmissionsChip
            key="submissions-info"
            formSubmissions={row.formSubmissions}
            isLoading={bookingSubmissionsQuery.isPending}
          />,
          ...generateIssuanceActions(row),
        ],
      },
    ]
  }, [
    driverQuery.data?.allDriversById,
    columnHelper,
    bookingSubmissionsQuery.isPending,
    generateIssuanceActions,
  ])

  const handleCloseCustomTab = useCallback(() => {
    setCurrentTab(tabs[0].value)
    setTabs((prev) => {
      const newTabs = [...prev]
      newTabs.pop()

      return newTabs
    })
    setCustomSelection('')
  }, [tabs])

  const handleCustomSelect = useCallback(
    (target: CustomTabs) => {
      setTabs((prev) => [
        ...prev,
        {
          label: `${ctIntl.formatMessage({ id: 'Custom' })} - ${ctIntl.formatMessage({
            id: target,
          })}`,
          value: 'custom',
          icon: (
            <CloseIcon
              onClick={handleCloseCustomTab}
              fontSize="small"
            />
          ),
          iconPosition: 'end',
        },
      ])

      setCurrentTab('custom')
      setCustomSelection(target)
    },
    [handleCloseCustomTab],
  )

  const filteredData = useMemo(
    () =>
      match(currentTab)
        .with(LIST_TABS.SCHEDULED, () =>
          issuanceList.filter((booking) =>
            [
              BookingStatus.BOOKING_STATUS_APPROVED,
              BookingStatus.BOOKING_STATUS_REQUESTED,
            ].includes(columnsGetters.statusId(booking) as BookingStatus),
          ),
        )
        .with(LIST_TABS.IN_PROGRESS, () =>
          issuanceList.filter((booking) =>
            [
              BookingStatus.BOOKING_STATUS_ACTIVE,
              BookingStatus.BOOKING_STATUS_ACTIVE_LATE,
            ].includes(columnsGetters.statusId(booking) as BookingStatus),
          ),
        )
        .with(LIST_TABS.HISTORY, () =>
          issuanceList.filter((booking) =>
            [
              BookingStatus.BOOKING_STATUS_CANCELLED,
              BookingStatus.BOOKING_STATUS_DECLINED,
              BookingStatus.BOOKING_STATUS_RETURNED,
              BookingStatus.BOOKING_STATUS_RETURNED_LATE,
              BookingStatus.BOOKING_STATUS_FORCE_TERMINATED,
            ].includes(columnsGetters.statusId(booking) as BookingStatus),
          ),
        )
        .with(LIST_TABS.CUSTOM, () =>
          issuanceList.filter((booking) => {
            const status = customSelection
              ? customTabAndStatusMapping[customSelection]
              : null
            return status ? columnsGetters.statusId(booking) === status : false
          }),
        )
        .otherwise(() => []),
    [currentTab, customSelection, issuanceList],
  )

  const getBulkActionOptions = () => {
    if (isEmpty(multiSelectedBookingIds)) {
      return []
    }

    const allStatus = uniq(
      issuanceList
        .filter((booking) => multiSelectedBookingIds.includes(booking.id))
        .map((booking) => columnsGetters.statusId(booking)),
    )

    if (!isEmpty(allStatus)) {
      if (
        allStatus.every((s) =>
          [BookingStatus.BOOKING_STATUS_APPROVED].includes(s as BookingStatus),
        )
      ) {
        return [
          <MenuItem
            key="change-to-active"
            onClick={() =>
              setCurrentModal({
                type: 'change_to_active',
                data: {
                  bookingIds: multiSelectedBookingIds,
                },
              })
            }
            disabled={!carpoolChangeBookingToActive}
          >
            {ctIntl.formatMessage({ id: 'carpool.list.changeToActive' })}
          </MenuItem>,
          <MenuItem
            key="cancel"
            onClick={() =>
              setCurrentModal({
                type: 'cancel_booking',
                data: { bookingIds: multiSelectedBookingIds },
              })
            }
            disabled={!carpoolCancelBooking}
          >
            {ctIntl.formatMessage({ id: 'Cancel' })}
          </MenuItem>,
        ]
      }

      if (
        allStatus.every((s) =>
          [BookingStatus.BOOKING_STATUS_REQUESTED].includes(s as BookingStatus),
        )
      ) {
        const canApproveBookings = issuanceList
          .filter((booking) => multiSelectedBookingIds.includes(booking.id))
          .every((booking) => canApproveBooking(booking.pendingManagers))
        return [
          <MenuItem
            key="approve"
            onClick={() =>
              setCurrentModal({
                type: 'approve_booking',
                data: { bookingIds: multiSelectedBookingIds },
              })
            }
            disabled={!carpoolApproveBookings || !canApproveBookings}
          >
            {ctIntl.formatMessage({ id: 'Approve' })}
          </MenuItem>,
          <MenuItem
            key="decline"
            onClick={() =>
              setCurrentModal({
                type: 'decline_booking',
                data: { bookingIds: multiSelectedBookingIds },
              })
            }
          >
            {ctIntl.formatMessage({ id: 'Decline' })}
          </MenuItem>,
        ]
      }
    }

    return []
  }

  const handleSelectionModelChange = (newSelectionModel: GridRowSelectionModel) => {
    setMultiSelectedBookingIds(newSelectionModel as Array<CarpoolBookingId>)
  }

  const listMetrics = useMemo(() => {
    if (!isEmpty(issuanceListQuery.data?.metrics)) {
      const metrics = (issuanceListQuery.data as FetchIssuanceList.Return).metrics

      return metricConfig.reduce(
        (acc, { key, label, tab }) => {
          if (metrics[key]) {
            acc.push({
              label,
              value: metrics[key],
              ...(tab && { onClick: () => handleCustomSelect(tab) }),
            })
          }
          return acc
        },
        [] as Array<{ label: string; value: number; onClick?: () => void }>,
      )
    }

    return []
  }, [issuanceListQuery.data, handleCustomSelect])

  const shouldShowCheckboxSelection =
    currentTab !== LIST_TABS.HISTORY &&
    ![
      CustomTabs.DECLINED,
      CustomTabs.CANCELLED,
      CustomTabs.RETURNED,
      CustomTabs.RETURNED_LATE,
    ].includes(customSelection as CustomTabs)

  const carpoolOptionsContextValue = useMemo(
    () => ({
      carpoolOptionsData:
        bookingOptionQuery.data ?? ({} as FetchCarpoolOptionsParsedData),
    }),
    [bookingOptionQuery.data],
  )

  return (
    <CarpoolOptionsContext.Provider value={carpoolOptionsContextValue}>
      <PageWithMainTableContainer>
        <Box
          sx={{
            display: 'flex',
            justifyContent: 'space-between',
            mb: 2,
          }}
        >
          <Typography variant="h5">
            {ctIntl.formatMessage(
              { id: 'carpool.list.header' },
              {
                values: {
                  carpoolAppName,
                },
              },
            )}
          </Typography>
          <Button
            startIcon={<AddIcon />}
            onClick={() => {
              history.push(
                `${path}/${DETAILS_PREFIX}?${buildRouteQueryString({
                  schema: issuanceRequestSearchParamsSchema,
                  searchParams: {
                    type: 'add',
                  },
                })}`,
              )
            }}
            color="primary"
            variant="contained"
          >
            {ctIntl.formatMessage(
              { id: 'carpool.newCarpoolRequest' },
              { values: { carpoolAppName } },
            )}
          </Button>
        </Box>
        <StatBar
          stats={listMetrics}
          isClickable={!customSelection}
          isLoading={issuanceListQuery.isPending}
        />

        <ContainerWithTabsForDataGrid
          renderTabs={() => (
            <ContainerWithTabsForDataGrid.Tabs
              value={currentTab}
              onChange={(_e, newValue) => setCurrentTab(newValue)}
            >
              {tabs.map(({ label, value, icon, iconPosition }) => (
                <ContainerWithTabsForDataGrid.Tab
                  key={value}
                  label={label}
                  value={value}
                  sx={{
                    height: '48px',
                    minHeight: '48px',
                  }}
                  {...(icon
                    ? {
                        icon,
                        iconPosition,
                      }
                    : {})}
                />
              ))}
            </ContainerWithTabsForDataGrid.Tabs>
          )}
        >
          <UserDataGridWithSavedSettingsOnIDB<IssuanceItem>
            Component={DataGridAsTabItem}
            dataGridId="currentIssuance"
            disableVirtualization
            disableRowSelectionOnClick
            loading={issuanceListQuery.isPending}
            autoPageSize
            pagination
            autosizeOnMount
            rows={filteredData}
            columns={columns}
            checkboxSelection={shouldShowCheckboxSelection}
            onRowSelectionModelChange={handleSelectionModelChange}
            rowSelectionModel={multiSelectedBookingIds}
            initialState={{
              pinnedColumns: {
                left: [GRID_CHECKBOX_SELECTION_COL_DEF.field, 'bookingNumber'],
                right: ['actions'],
              },
            }}
            slots={{
              toolbar: GridToolbarWithQuickFilter,
              loadingOverlay: LinearProgress,
            }}
            slotProps={{
              toolbar: GridToolbarWithQuickFilter.createProps({
                gridToolbarRightContent: shouldShowCheckboxSelection ? (
                  <PopupState
                    variant="popover"
                    popupId="bulk-action"
                  >
                    {(popupState) => {
                      const builkActionOptions = getBulkActionOptions()
                      return (
                        <div>
                          <Button
                            size="small"
                            variant="outlined"
                            color="secondary"
                            sx={{
                              width: '30px',
                              height: '30px',
                              minWidth: 'auto',
                            }}
                            disabled={isEmpty(builkActionOptions)}
                            {...bindTrigger(popupState)}
                          >
                            <ThreeDotsIcon />
                          </Button>
                          <Popover
                            {...bindPopover(popupState)}
                            anchorOrigin={{
                              vertical: 'bottom',
                              horizontal: 'center',
                            }}
                            transformOrigin={{
                              vertical: 'top',
                              horizontal: 'center',
                            }}
                          >
                            <MenuList>
                              {builkActionOptions.map((item) => item)}
                            </MenuList>
                          </Popover>
                        </div>
                      )
                    }}
                  </PopupState>
                ) : null,
              }),
            }}
          />
        </ContainerWithTabsForDataGrid>

        {/* Modal */}
        {match(currentModal)
          .with(null, () => null)
          .with({ type: 'change_to_active' }, ({ data }) => (
            <ChangeToActiveConfirmationModal
              onClose={() => setCurrentModal(null)}
              bookingIds={data.bookingIds}
            />
          ))
          .with({ type: 'key_collection' }, ({ data }) => (
            <KeyCollectionModal
              onClose={() => setCurrentModal(null)}
              bookingId={data.bookingId}
              initialValues={data.initialValues}
            />
          ))
          .with({ type: 'key_return' }, ({ data }) => (
            <KeyReturnModal
              onClose={() => setCurrentModal(null)}
              bookingId={data.bookingId}
              initialValues={data.initialValues}
            />
          ))
          .with({ type: 'approve_booking' }, ({ data }) => (
            <ApproveBookingModal
              onClose={() => setCurrentModal(null)}
              bookingIds={data.bookingIds}
            />
          ))
          .with({ type: 'complete_booking' }, ({ data }) => (
            <CompleteBookingModal
              onClose={() => setCurrentModal(null)}
              bookingIds={data.bookingIds}
            />
          ))
          .with({ type: 'decline_booking' }, ({ data }) => (
            <DeclineBookingModal
              onClose={() => setCurrentModal(null)}
              bookingIds={data.bookingIds}
            />
          ))
          .with({ type: 'cancel_booking' }, ({ data }) => (
            <CancelBookingModal
              onClose={() => setCurrentModal(null)}
              bookingIds={data.bookingIds}
            />
          ))
          .with({ type: 'force_terminate_booking' }, ({ data }) => (
            <ForceTerminateBookingModal
              onClose={() => setCurrentModal(null)}
              bookingIds={data.bookingIds}
            />
          ))
          .exhaustive()}

        {/* Drawer */}
        {currentDrawer?.type === 'booking_details' && (
          <BookingDetailsDrawer
            onClose={() => setCurrentDrawer(null)}
            bookingId={currentDrawer.data.bookingId}
            vehicleTimelineParams={currentDrawer.data.vehicleTimelineParams}
          />
        )}
      </PageWithMainTableContainer>
    </CarpoolOptionsContext.Provider>
  )
}

export default SpfList
