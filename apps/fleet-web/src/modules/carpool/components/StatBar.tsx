import { Stack, styled, Typography } from '@karoo-ui/core'

import { getCompanyName } from 'duxs/user-sensitive-selectors'
import { useTypedSelector } from 'src/redux-hooks'
import Spinner from 'src/util-components/spinner'

import { ctIntl } from 'cartrack-ui-kit'

type Props = {
  stats: Array<{
    label: string
    value: number | string
    onClick?: () => void
  }>
  isClickable?: boolean
  isLoading: boolean
}

const StatBar = ({ stats, isClickable = false, isLoading }: Props) => {
  const companyName = useTypedSelector(getCompanyName)
  const isScdf = companyName === 'SCDF'

  return (
    <Container isScdf={isScdf}>
      {isLoading ? (
        <Spinner />
      ) : (
        stats.map((stat) => (
          <StatBlock
            key={stat.label}
            onClick={stat.onClick}
            sx={{ cursor: 'pointer', pointerEvents: isClickable ? 'auto' : 'none' }}
          >
            <Typography
              variant="overline"
              sx={{
                lineHeight: 1.5,
                whiteSpace: 'nowrap',
                overflow: 'hidden',
                textOverflow: 'ellipsis',
              }}
              title={ctIntl.formatMessage({ id: stat.label })}
            >
              {ctIntl.formatMessage({ id: stat.label })}
            </Typography>
            <Typography
              variant="caption"
              sx={{ lineHeight: 1.5 }}
            >
              {stat.value}
            </Typography>
          </StatBlock>
        ))
      )}
    </Container>
  )
}

export default StatBar

const Container = styled('div', {
  shouldForwardProp: (prop) => prop !== 'isScdf',
})<{ isScdf: boolean }>(({ theme, isScdf }) =>
  theme.unstable_sx({
    display: 'flex',
    padding: 2,
    backgroundColor: isScdf ? theme.palette.primary.dark : theme.palette.secondary.main,
    color: theme.palette.primary.contrastText,
    borderRadius: '4px',
    height: 'min-content',
    justifyContent: 'space-between',
    overflowX: 'auto',
  }),
)

const StatBlock = styled(Stack)(() => ({
  display: 'flex',
  flexDirection: 'column',
  fontSize: '12px',
  textTransform: 'uppercase',
}))
