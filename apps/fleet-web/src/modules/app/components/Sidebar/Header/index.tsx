import { memo } from 'react'
import { Icon<PERSON>utton, Stack, styled } from '@karoo-ui/core'
import ExpandMore from '@mui/icons-material/ExpandMore'
import { useHistory } from 'react-router'

import {
  getClientMenuLogoPathMeta,
  getClientSidebarMenuHeaderIconPathMeta,
  getUserSettingStyleProperties,
} from 'duxs/user'
import { getAppMainUrl } from 'duxs/user-route-selectors'
import type { EventHandlerBranded } from 'src/hooks/useEventHandler'
import { useTypedSelector } from 'src/redux-hooks'
import { GA4 } from 'src/shared/google-analytics4'

import { ImageWithFallback } from 'cartrack-ui-kit'

type Props = {
  isExpanded: boolean
  isHovered: boolean
  isSharedMapPage: boolean
  onCollapseExpandChange: EventHandlerBranded<[]>
}

const SidebarHeader = ({
  isExpanded,
  isHovered,
  isSharedMapPage,
  onCollapseExpandChange,
}: Props) => (
  <SidebarHeaderContainer isExpanded={isExpanded}>
    {(() => {
      if (isExpanded) {
        return (
          <>
            <SidebarHeaderLogo
              isExpanded={isExpanded}
              isSharedMapPage={isSharedMapPage}
            />

            <SidebarHeaderCollapseExpandIconButton
              isExpanded={isExpanded}
              onCollapseExpandChange={onCollapseExpandChange}
            />
          </>
        )
      }

      if (isHovered) {
        return (
          <SidebarHeaderCollapseExpandIconButton
            isExpanded={isExpanded}
            onCollapseExpandChange={onCollapseExpandChange}
          />
        )
      }

      return (
        <SidebarHeaderLogo
          isExpanded={isExpanded}
          isSharedMapPage={isSharedMapPage}
        />
      )
    })()}
  </SidebarHeaderContainer>
)

export default memo(SidebarHeader)

const SidebarHeaderContainer = styled(Stack, {
  shouldForwardProp: (prop) => prop !== 'isExpanded',
})<{ isExpanded: Props['isExpanded'] }>(({ isExpanded, theme }) =>
  theme.unstable_sx({
    py: 2,
    pr: 1,
    pl: isExpanded ? 2 : 1,
    gap: 1,
    justifyContent: isExpanded ? 'space-between' : 'center',
    alignItems: 'center',
    flexDirection: 'row',
  }),
)

const SidebarHeaderLogo = ({
  isExpanded,
  isSharedMapPage,
}: {
  isExpanded: Props['isExpanded']
  isSharedMapPage: Props['isSharedMapPage']
}) => {
  const appMainUrl = useTypedSelector(getAppMainUrl)
  const history = useHistory()
  const menuLogoMeta = useTypedSelector(getClientMenuLogoPathMeta)
  const menuHeaderIconMeta = useTypedSelector(getClientSidebarMenuHeaderIconPathMeta)

  const logoMeta = isExpanded ? menuLogoMeta : menuHeaderIconMeta

  return (
    <Stack
      direction="row"
      justifyContent="center"
      alignItems="center"
      height="36px"
      width="100%"
      onClick={() => {
        if (!isSharedMapPage) {
          GA4.event({
            category: 'Sidebar',
            action: 'Header - Logo click',
          })

          history.push(appMainUrl)
        }
      }}
    >
      <ImageWithFallback
        src={logoMeta.computedPath}
        fallbackSrc={logoMeta.fallbackPath}
        alt="sidebar-header-logo"
        style={{
          maxHeight: '100%',
          maxWidth: '100%',
        }}
      />
    </Stack>
  )
}

const SidebarHeaderCollapseExpandIconButton = ({
  isExpanded,
  onCollapseExpandChange,
}: {
  isExpanded: Props['isExpanded']
  onCollapseExpandChange: Props['onCollapseExpandChange']
}) => {
  const { styleNavbarColour, styleSidebarMenuHoverColour } = useTypedSelector(
    getUserSettingStyleProperties,
  )
  return (
    <CollapseExpandIconButton
      size="small"
      onClick={onCollapseExpandChange}
      sx={{
        backgroundColor: styleNavbarColour,
        '&:hover': { backgroundColor: styleSidebarMenuHoverColour },
      }}
    >
      <ExpandMoreIcon
        fontSize="small"
        isExpanded={isExpanded}
      />
    </CollapseExpandIconButton>
  )
}

const ExpandMoreIcon = styled(ExpandMore, {
  shouldForwardProp: (prop) => prop !== 'isExpanded',
})<{ isExpanded: Props['isExpanded'] }>(({ isExpanded }) => ({
  transform: `rotate(${isExpanded ? '+' : '-'}90deg)`,
}))

const CollapseExpandIconButton = styled(IconButton)(({ theme }) => ({
  color: 'inherit',
  padding: theme.spacing(1),
  borderRadius: theme.spacing(0.5),
  outline: `1px solid ${theme.palette.secondary.light} !important`,

  '&:hover': {
    color: theme.palette.primary.contrastText,
  },
}))
