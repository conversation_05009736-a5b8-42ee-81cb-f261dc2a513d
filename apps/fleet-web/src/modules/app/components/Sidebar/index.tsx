import { memo, useMemo, useState } from 'react'
import { isEmpty, isFunction, kebabCase, size } from 'lodash'
import { Stack, styled } from '@karoo-ui/core'
import ExpandMore from '@mui/icons-material/ExpandMore'
import {
  Menu,
  menuClasses,
  Sidebar,
  sidebarClasses,
  type MenuItemProps,
} from 'react-pro-sidebar'
import { matchPath, NavLink, Redirect, useLocation } from 'react-router-dom'
import * as R from 'remeda'
import { match } from 'ts-pattern'
import { z } from 'zod'

import { getMapSubTabs } from 'duxs/map'
import { getUserSettingStyleProperties, type LoggedInBaseUser } from 'duxs/user'
import { getAppMainUrl, shouldBeVisible } from 'duxs/user-route-selectors'
import { useEventHandlerBranded } from 'src/hooks/useEventHandler'
import type { IdbStateSetter } from 'src/hooks/useUserIdbState'
import { useUserIdbStateWithReduxSync } from 'src/hooks/useUserIdbStateWithReduxSync'
import {
  ADMIN,
  ALERT_CENTER,
  ALERTS,
  CARPOOL,
  COACHING,
  COSTS,
  createTab,
  DASHBOARD,
  ENGINE_ALERTS,
  FIELD_SERVICE,
  getVisionSubMenusMetaData,
  HELP,
  KNOW_THE_DRIVER,
  LIST,
  MAINTENANCE,
  MAP,
  MESSAGING,
  PRIVACY,
  REPORTS,
  SETTINGS,
  TACHOGRAPH,
  VISION,
} from 'src/modules/app/components/routes'
import { Flag } from 'src/modules/components/connected'
import { useUserAvailableCameraTerminalTypes } from 'src/modules/vision/api/queries'
import { useTypedSelector } from 'src/redux-hooks'
import { Array_reduce } from 'src/util-functions/performance-critical-utils'

import { onMapPath } from 'cartrack-utils'
import type { Route, SubTab, Tab } from '../routes/types'
import SidebarClock from './Clock'
import SidebarFooter from './Footer'
import SidebarHeader from './Header'
import SidebarMenuIcon from './Menu/Icon'
import SidebarMenuItem from './Menu/Item'
import SidebarSubMenu, { type SubMenuDetails } from './Menu/SubMenu'
import { SIDEBAR_EXPANDED_KEY_STRING } from './useCollapseSidebar'

type Props = {
  currentPath: string
  user: LoggedInBaseUser
}

const CtSidebar = ({ currentPath, user }: Props) => {
  const sidebarExpandedState = useUserIdbStateWithReduxSync({
    stateKey: SIDEBAR_EXPANDED_KEY_STRING,
    getZodSchema: () => z.boolean(),
    defaultValue: true,
    keepStateOnIdbAfterLogout: true,
  })

  if (sidebarExpandedState === 'loading') {
    return null
  }

  return (
    <Content
      currentPath={currentPath}
      user={user}
      isSidebarExpandedFromIdb={sidebarExpandedState[0]}
      setIsSidebarExpandedFromIdb={sidebarExpandedState[1]}
    />
  )
}

type ContentProps = {
  currentPath: string
  isSidebarExpandedFromIdb: boolean
  setIsSidebarExpandedFromIdb: IdbStateSetter<boolean>
  user: LoggedInBaseUser
}

const Content = ({
  isSidebarExpandedFromIdb,
  setIsSidebarExpandedFromIdb,
  currentPath,
  user,
}: ContentProps) => {
  const location = useLocation()
  const appState = useTypedSelector((state) => state)
  const mapSubTabsNames = useTypedSelector(getMapSubTabs)
  const mainUrl = useTypedSelector(getAppMainUrl)
  const { karooUiTheme, ...userStyleSettings } = useTypedSelector(
    getUserSettingStyleProperties,
  )
  const userAvailableCameraTerminalTypesQuery = useUserAvailableCameraTerminalTypes()

  const [isHovered, setIsHovered] = useState(false)

  const tabs = useMemo((): Array<Tab> => {
    const rawData = createTab({
      id: 'RAW',
      text: 'raw data',
      meta: true,
      path: '/raw',
      flag: 'raw',
    })

    const tabs: Array<Tab> = [
      MAP.tab,
      rawData,
      LIST.tab,
      SETTINGS.tab,
      ALERTS.tab,
      DASHBOARD.tab,
      REPORTS.tab,
      COSTS.tab,
      TACHOGRAPH.tab,
      PRIVACY.tab,
      MESSAGING.tab,
      ENGINE_ALERTS.tab,
      HELP.tab,
    ]

    // RMM: Temporary handling of when to show raw data page. Should be moved to settings later on.
    if (user && /^grab/gi.test(user.username)) {
      tabs.push(rawData)
    }

    const visionTab = match(
      getVisionSubMenusMetaData({ userAvailableCameraTerminalTypesQuery }),
    )
      .returnType<Tab>()
      .with('error', () => ({
        ...VISION.tab,
        subMenus: [],
      }))
      .with('pending', () => ({
        ...VISION.tab,
        subMenus: [], // Return empty array during loading to display the loading icon instead of the expand icon
        showLoadingIcon: true,
      }))
      .with('redirect_landing_page', () => ({
        ...VISION.tab,
        subMenus: [],
      }))
      .with('success', () => VISION.tab)
      .exhaustive()

    return [
      ...tabs,
      FIELD_SERVICE.tab,
      CARPOOL.tab,
      visionTab,
      KNOW_THE_DRIVER.tab,
      ALERT_CENTER.tab,
      COACHING.tab,
      MAINTENANCE.tab,
      ADMIN.tab,
    ]
  }, [user, userAvailableCameraTerminalTypesQuery])

  const handleMouseEnter = () => {
    setIsHovered(true)
  }

  const handleMouseLeave = () => {
    setIsHovered(false)
  }

  const isFullscreen =
    currentPath === '/map/fullscreen' ||
    currentPath === MAP.VEHICLE_CLUSTERS_MAP_PAGE.path

  const isSharedMapPage = currentPath === MAP.SHARED_APP.path

  const isSidebarExpanded =
    isFullscreen || isSharedMapPage ? false : isSidebarExpandedFromIdb

  const visibleTabs = useMemo(() => tabs.filter((tab) => !!tab && !tab.meta), [tabs])

  const handleCollapseExpandChange = useEventHandlerBranded(() => {
    setIsSidebarExpandedFromIdb((prevState) => !prevState)
  })

  const getSubTabs = (tab: Tab) => {
    const { subTabs } = tab
    const tabs = isFunction(subTabs) ? subTabs(appState) : subTabs

    return tabs ?? []
  }

  const getFirstAvailableSubTabPath = (subTabs: Array<SubTab> = []) => {
    const subTab = subTabs.find(({ flag, selector }) =>
      shouldBeVisible(appState, { selector, setting: flag }),
    )

    if (subTab === undefined) {
      return ''
    }

    return subTab.navigationPath || subTab.path || ''
  }

  const isCurrentPathOneOf = (routesObject: Record<string, Route>) =>
    Object.keys(routesObject).some(
      (routeName) =>
        matchPath(currentPath, {
          path: routesObject[routeName].path,
          exact: true,
        }) !== null,
    )

  const getSelectedSubTab = (tab: Tab, subTabs: Array<SubTab>, currentPath: string) => {
    // Works for now but should be improved (maybe with a new version of react-router, we don't have to do this type of comparison)
    let fallbackMatch

    for (const subTab of subTabs) {
      if (isEmpty(subTab)) {
        continue
      }

      // Sub tab's other tabs
      const { subRoutes, tabRoutes } = subTab
      // Object forms
      if (tabRoutes && isCurrentPathOneOf(tabRoutes)) {
        return subTab
      }

      if (subRoutes && isCurrentPathOneOf(subRoutes)) {
        return subTab
      }

      if (subTab.path === currentPath) {
        return subTab
      }

      if (
        subTab.path === currentPath.slice(0, size(subTab.path)) ||
        subTab.path ===
          currentPath.slice(size(tab.path), size(tab.path) + size(subTab.path))
      ) {
        // Cases for routes that have params (e.g vehicles/:vehicleId) so we abstract those (:vehicleId) when comparing
        fallbackMatch = subTab
      }
    }

    return fallbackMatch
  }

  const selectedTab = (() => {
    let selectedTab = {}

    if (isFullscreen) {
      return selectedTab
    } else {
      for (const tab of tabs) {
        const subTabs = getSubTabs(tab)

        if (tab.path && tab.path === currentPath.slice(0, tab.path.length)) {
          selectedTab = { ...tab, subTabs }
        }

        if (!subTabs) {
          continue
        }

        const selectedSubTab = getSelectedSubTab(tab, subTabs, currentPath)

        if (selectedSubTab) {
          return {
            ...tab,
            subTabs,
            selectedSubTab,
          }
        }
      }
    }

    return selectedTab
  })() as Tab & {
    selectedSubTab: SubTab | undefined
    subTabs: Array<SubTab>
  }

  if (!selectedTab) {
    console.error(
      `[Cartrack] - The path "${currentPath}" does not match any paths on your Sidebar configuration`,
    )
    return <Redirect to={mainUrl} />
  }

  return (
    <Stack
      onMouseEnter={handleMouseEnter}
      onMouseLeave={handleMouseLeave}
    >
      <StyledSidebar
        width="220px"
        collapsedWidth="68px"
        collapsed={!isSidebarExpanded}
        userStyleSettings={userStyleSettings}
      >
        {/* Header */}
        <SidebarHeader
          isExpanded={isSidebarExpanded}
          isHovered={isHovered && !isFullscreen && !isSharedMapPage}
          isSharedMapPage={isSharedMapPage}
          onCollapseExpandChange={handleCollapseExpandChange}
        />

        {(() => {
          if (isFullscreen) {
            return <SidebarClock />
          }

          if (isSharedMapPage) {
            return null
          }

          // Menus List
          return (
            <Menu
              renderExpandIcon={({ open }) =>
                isSidebarExpanded ? (
                  <ExpandMore
                    fontSize="small"
                    sx={{
                      transform: `rotate(${open ? '180' : '0'}deg)`,
                    }}
                  />
                ) : null
              }
            >
              {!isFullscreen &&
                visibleTabs.map((tab) => {
                  const { id, path, text, icon, flag, selector, withBetaBadge } = tab

                  const subTabs = getSubTabs(tab)

                  const [mapSubtab] = mapSubTabsNames
                  const mainPath = onMapPath(path || '')
                    ? `${path}/${kebabCase(mapSubtab)}`
                    : path || getFirstAvailableSubTabPath(subTabs)

                  const isTabActive = (() => {
                    const { selectedSubTab } = selectedTab

                    if (R.isArray(subTabs) && selectedSubTab) {
                      return subTabs.some(({ path }) => path === selectedSubTab.path)
                    }

                    return path === selectedTab.path
                  })()

                  const mainMenuFinalText =
                    typeof text === 'string' ? text : text(appState)

                  const commonProps = {
                    id: id ? id : undefined,
                    ...(id ? { 'data-testid': `menu-${id}` } : {}),
                    icon: icon ? <SidebarMenuIcon Icon={icon} /> : null,
                  }

                  const mainMenuWithBetaBadge =
                    typeof withBetaBadge === 'function'
                      ? withBetaBadge(appState)
                      : withBetaBadge ?? false

                  const hasSubMenus =
                    !isEmpty(subTabs) ||
                    !isEmpty(
                      isFunction(tab.subMenus) ? tab.subMenus(appState) : tab.subMenus,
                    )

                  const subMenuDefaultOpen = tab.subMenus
                    ? matchPath(location.pathname, {
                        path: tab.path,
                        exact: false,
                      }) !== null
                    : isTabActive

                  if (isTabActive) {
                    console.log('isTabActive', isTabActive, selectedTab, subTabs, tab)
                  }

                  const subMenusDetails = tab.subMenus
                    ? Array_reduce(
                        isFunction(tab.subMenus)
                          ? tab.subMenus(appState)
                          : tab.subMenus,
                        [],
                        (acc: Array<SubMenuDetails>, subMenu) => {
                          const title =
                            typeof subMenu.text === 'string'
                              ? subMenu.text
                              : subMenu.text(appState)

                          acc.push({
                            path: subMenu.path,
                            flag: undefined,
                            selector: subMenu.selector,
                            title,
                            withBetaBadge:
                              typeof subMenu.withBetaBadge === 'function'
                                ? subMenu.withBetaBadge(appState)
                                : subMenu.withBetaBadge ?? false,
                            hidden: undefined,
                            menuItemProps: {
                              'data-testid': `submenu-${kebabCase(title)}`,
                              component: <NavLink to={subMenu.path} />,
                            } as MenuItemProps,
                          })

                          return acc
                        },
                      )
                    : []

                  const subTabsDetails = Array_reduce(
                    subTabs,
                    [],
                    (acc: Array<SubMenuDetails>, subTab) => {
                      const title =
                        typeof subTab.text === 'string'
                          ? subTab.text
                          : subTab.text(appState)

                      acc.push({
                        path: subTab.navigationPath || subTab.path,
                        flag: subTab.flag,
                        selector: subTab.selector,
                        title,
                        withBetaBadge:
                          typeof subTab.withBetaBadge === 'function'
                            ? subTab.withBetaBadge(appState)
                            : subTab.withBetaBadge ?? false,
                        hidden: subTab.hidden,
                        menuItemProps: {
                          'data-testid': `submenu-${kebabCase(title)}`,
                          component: (
                            <NavLink
                              to={subTab.navigationPath || subTab.path}
                              exact
                              {...(subTab === selectedTab.selectedSubTab
                                ? { isActive: () => true }
                                : {})}
                            />
                          ),
                        } as MenuItemProps,
                      })

                      return acc
                    },
                  )

                  return (
                    <Flag
                      key={id}
                      setting={flag}
                      selector={selector}
                    >
                      {hasSubMenus ? (
                        <SidebarSubMenu
                          mainPath={mainPath}
                          isExpanded={isSidebarExpanded}
                          mainMenuTitle={mainMenuFinalText}
                          mainMenuWithBetaBadge={mainMenuWithBetaBadge}
                          subMenusDetails={
                            tab.subMenus ? subMenusDetails : subTabsDetails
                          }
                          commonProps={commonProps}
                          subMenuDefaultOpen={subMenuDefaultOpen}
                        />
                      ) : (
                        <SidebarMenuItem
                          title={mainMenuFinalText}
                          withBetaBadge={mainMenuWithBetaBadge}
                          menuItemProps={{
                            ...commonProps,
                            component: <NavLink to={mainPath} />,
                          }}
                          showLoadingIcon={tab.showLoadingIcon}
                        />
                      )}
                    </Flag>
                  )
                })}
            </Menu>
          )
        })()}

        {!isSharedMapPage && (
          // Footer
          <SidebarFooter
            isExpanded={isSidebarExpanded}
            isFullscreen={isFullscreen}
          />
        )}
      </StyledSidebar>
    </Stack>
  )
}

export default memo(CtSidebar)

const StyledSidebar = styled(Sidebar)<{
  userStyleSettings: ReturnType<typeof getUserSettingStyleProperties>
}>(({ theme, userStyleSettings }) => ({
  height: '100%',
  display: 'flex',
  flexDirection: 'column',
  border: 'none !important',

  [`.${sidebarClasses.container}`]: {
    display: 'flex',
    flexDirection: 'column',
    backgroundColor: userStyleSettings.styleNavbarColour,
    color: `${userStyleSettings.styleSidebarMenuInactiveFontColour} !important`,
    overflow: 'hidden',

    [`.${menuClasses.root}`]: {
      flex: 1,
      overflowY: 'scroll',
      overflowX: 'hidden',
      padding: theme.spacing(1),
      paddingRight: 0,
    },
  },

  [`.${menuClasses.subMenuRoot}:not(.${menuClasses.open}):has(.active)`]: {
    [`& > .${menuClasses.button}:first-of-type`]: {
      color: userStyleSettings.styleSidebarMenuActiveFontColour,
      backgroundColor: `${userStyleSettings.styleSidebarMenuActiveColour} !important`,
    },
  },

  // All menu links
  [`.${menuClasses.button}`]: {
    height: '36px !important',
    padding: `${theme.spacing(1)} !important`,
    borderRadius: `${theme.spacing(0.5)} !important`,

    ':hover': {
      color: userStyleSettings.styleSidebarMenuHoverFontColour,
      backgroundColor: `${userStyleSettings.styleSidebarMenuHoverColour} !important`,
    },

    [`&.active`]: {
      // Active menu
      color: userStyleSettings.styleSidebarMenuActiveFontColour,
      backgroundColor: `${userStyleSettings.styleSidebarMenuActiveColour} !important`,
    },
  },

  [`.${menuClasses.icon}`]: {
    width: '20px',
    height: '20px',
    minWidth: '20px',
    minHeight: '20px',
    marginRight: `${theme.spacing(2)} !important`,
  },

  [`&.${sidebarClasses.collapsed}`]: {
    [`.${menuClasses.subMenuRoot}:has(.active)`]: {
      [`& > .${menuClasses.button}:first-of-type`]: {
        color: userStyleSettings.styleSidebarMenuActiveFontColour,
        backgroundColor: `${userStyleSettings.styleSidebarMenuActiveColour} !important`,
      },
    },

    [`& .${menuClasses.icon}`]: {
      marginLeft: `${theme.spacing(1)} !important`,
    },

    [`.${menuClasses.menuItemRoot} .${menuClasses.subMenuContent}`]: {
      display: 'none !important',
    },
  },

  // Expand icons
  [`.${menuClasses.SubMenuExpandIcon}`]: {
    height: '100%',
  },

  // Submenu container
  [`.${menuClasses.subMenuContent}`]: {
    transition: 'height 200ms !important',
    overflowX: 'hidden',
    backgroundColor: userStyleSettings.styleNavbarColour,
    color: userStyleSettings.styleSidebarSubMenuInactiveFontColour,

    // Submenu links
    [`& .${menuClasses.button}`]: {
      paddingRight: `${theme.spacing(2)} !important`,
      paddingLeft: `${theme.spacing(2)} !important`,
      marginLeft: `${theme.spacing(3.5)} !important`,
    },
  },
}))
