import { useRef, useState } from 'react'
import {
  MenuItem,
  Paper,
  Stack,
  styled,
  Tooltip,
  tooltipClasses,
  Typography,
  type TooltipProps,
} from '@karoo-ui/core'
import inView from 'in-view'
import { SubMenu, type MenuItemProps } from 'react-pro-sidebar'
import { NavLink } from 'react-router-dom'

import { getVisibleSubMenus } from 'src/duxs/sub-menu-selector'
import { Flag } from 'src/modules/components/connected'
import { useTypedSelector } from 'src/redux-hooks'
import type { AppState } from 'src/root-reducer'
import { ctIntl } from 'src/util-components/ctIntl'

import type { Selector } from '../../../routes/types'
import ContainerWithBetaBadge from '../ContainerWithBetaBadge'
import SidebarMenuItem from '../Item'
import SidebarMenuTitle from '../Title'

export type SubMenuDetails = {
  path: string
  flag?: string
  selector?: Selector
  title: string
  withBetaBadge: boolean
  hidden?: boolean
  menuItemProps: MenuItemProps
}

type Props = {
  mainPath: string
  isExpanded: boolean
  mainMenuTitle: string
  mainMenuWithBetaBadge: boolean
  subMenusDetails: Array<SubMenuDetails>
  commonProps: any
  subMenuDefaultOpen: boolean
}

const HEADER_PADDING_TOP_PX = 12
const HEADER_PADDING_BOTTOM_PX = 8
const HEADER_HEIGHT_PX = HEADER_PADDING_TOP_PX + HEADER_PADDING_BOTTOM_PX + 18

const SidebarSubMenu = ({
  mainPath,
  isExpanded,
  mainMenuTitle,
  mainMenuWithBetaBadge,
  subMenusDetails,
  commonProps,
  subMenuDefaultOpen,
}: Props) => {
  const [isSubMenuOpen, setIsSubMenuOpen] = useState(subMenuDefaultOpen)
  const [hasSubMenuFinishedClosing, setHasSubMenuFinishedClosing] = useState(true)
  const subMenuListRef = useRef<HTMLDivElement>(null)

  const visibleSubMenus = useTypedSelector((state: AppState) =>
    getVisibleSubMenus<SubMenuDetails>()(state, subMenusDetails),
  )

  const handleOpenChange = (open: boolean) => {
    setIsSubMenuOpen(open)
    if (open) {
      setHasSubMenuFinishedClosing(false)
    } else {
      // Wait for the submenu to close before displaying the tooltip so it can be properly aligned
      window.setTimeout(() => {
        setHasSubMenuFinishedClosing(true)
      }, 250)
    }
  }

  const handleSubMenuClick = () => {
    if (
      !isSubMenuOpen &&
      subMenuListRef.current !== null &&
      !inView.is(subMenuListRef.current)
    ) {
      window.setTimeout(() => {
        // Scroll to make the submenu visible
        if (subMenuListRef.current !== null) {
          subMenuListRef.current?.scrollIntoView({
            behavior: 'smooth',
            block: 'nearest',
            inline: 'nearest',
          })
        }
      }, 250)
    }
  }

  return (
    <StyledTooltip
      placement="right-start"
      slotProps={{
        popper: {
          modifiers: [{ name: 'offset', options: { offset: [-HEADER_HEIGHT_PX, 0] } }],
        },
      }}
      title={
        !isExpanded || (isExpanded && !isSubMenuOpen && hasSubMenuFinishedClosing) ? (
          <Paper
            elevation={8}
            sx={{
              paddingBottom: 1,
              gap: 0.5,
            }}
          >
            <Stack
              direction="row"
              sx={{
                px: 2,
                pt: `${HEADER_PADDING_TOP_PX}px`,
                pb: `${HEADER_PADDING_BOTTOM_PX}px`,
                height: `${HEADER_HEIGHT_PX}px`,
              }}
            >
              <ContainerWithBetaBadge withBetaBadge={mainMenuWithBetaBadge}>
                <Typography
                  variant="body2"
                  sx={{
                    fontSize: '10px',
                    fontStyle: 'normal',
                    fontWeight: 500,
                    color: 'secondary.light',
                  }}
                >
                  {ctIntl.formatMessage({ id: mainMenuTitle })}
                </Typography>
              </ContainerWithBetaBadge>
            </Stack>

            <Stack>
              {subMenusDetails.map(
                (subMenu) =>
                  !subMenu.hidden && (
                    <Flag
                      key={subMenu.path}
                      setting={subMenu.flag}
                      selector={subMenu.selector}
                    >
                      <NavLink
                        to={subMenu.path}
                        activeStyle={{
                          backgroundColor: 'lightgrey',
                        }}
                      >
                        <MenuItem
                          dense
                          sx={{
                            pl: 0,
                            color: 'primary.dark',
                            paddingLeft: 2,
                            paddingRight: 2,
                          }}
                        >
                          <ContainerWithBetaBadge withBetaBadge={subMenu.withBetaBadge}>
                            <Typography
                              variant="body2"
                              sx={{
                                color: 'secondary.dark',
                              }}
                            >
                              {ctIntl.formatMessage({ id: subMenu.title })}
                            </Typography>
                          </ContainerWithBetaBadge>
                        </MenuItem>
                      </NavLink>
                    </Flag>
                  ),
              )}
            </Stack>
          </Paper>
        ) : null
      }
    >
      <span>
        <SubMenu
          {...commonProps}
          onOpenChange={handleOpenChange}
          label={
            <ContainerWithBetaBadge withBetaBadge={mainMenuWithBetaBadge}>
              <SidebarMenuTitle title={mainMenuTitle} />
            </ContainerWithBetaBadge>
          }
          onClick={handleSubMenuClick}
          open={isSubMenuOpen}
          {...(!isExpanded || visibleSubMenus.length === 1
            ? { component: <NavLink to={mainPath} /> }
            : {})}
        >
          <Stack ref={subMenuListRef}>
            {subMenusDetails.map(
              (subMenu) =>
                !subMenu.hidden && (
                  <Flag
                    key={subMenu.path}
                    setting={subMenu.flag}
                    selector={subMenu.selector}
                  >
                    <SidebarMenuItem
                      title={subMenu.title}
                      withBetaBadge={subMenu.withBetaBadge}
                      menuItemProps={subMenu.menuItemProps}
                    />
                  </Flag>
                ),
            )}
          </Stack>
        </SubMenu>
      </span>
    </StyledTooltip>
  )
}

export default SidebarSubMenu

const StyledTooltip = styled(({ className, ...props }: TooltipProps) => (
  <Tooltip
    {...props}
    classes={{ popper: className }}
    disableFocusListener
    arrow={false}
  />
))(() => ({
  [`& .${tooltipClasses.tooltip}`]: {
    backgroundColor: 'none',
    padding: 0,
    // margin: '20px', Add this if an area to avoid closing on accident is needed
  },
  [`& .${tooltipClasses.arrow}`]: {
    color: 'white',
  },
}))
